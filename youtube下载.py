import yt_dlp
import os

def create_folder(folder_path):
    """在桌面创建一个文件夹，如果不存在则创建"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"文件夹 '{folder_path}' 已创建！")
    else:
        print(f"文件夹 '{folder_path}' 已存在！")

def download_video(url, folder_path):
    # 确保下载文件保存在目标文件夹
    output_template = os.path.join(folder_path, '%(title)s.%(ext)s')

    # FFmpeg 路径（项目目录中的 ffmpeg 文件夹）
    ffmpeg_path = os.path.join(os.path.dirname(__file__), "ffmpeg", "bin", "ffmpeg.exe")

    # Cookies 文件路径
    cookies_path = os.path.join(os.path.dirname(__file__), "youtube_cookies.txt")

    # 检查是否需要H.264转换的函数
    def get_postprocessors():
        return [
            {
                'key': 'FFmpegVideoConvertor',
                'preferedformat': 'mp4',
            }
        ]

    # 配置 yt-dlp 下载参数 - 使用更稳定的配置
    ydl_opts = {
        # 简化格式选择，优先H.264编码(PR兼容)
        'format': 'bestvideo[height<=1080][vcodec*=avc1]+bestaudio/bestvideo[height<=1080][vcodec*=h264]+bestaudio/bestvideo[height<=1080]+bestaudio/best[height<=1080]/best',
        'merge_output_format': 'mp4',  # 输出为 mp4 格式
        'outtmpl': output_template,  # 保存路径
        'noplaylist': True,  # 不下载整个播放列表
        'quiet': False,  # 显示详细输出（调试时有帮助）
        'ffmpeg_location': ffmpeg_path,  # 手动指定 FFmpeg 路径

        # 使用cookies文件（如果存在）
        'cookiefile': cookies_path if os.path.exists(cookies_path) else None,

        # 网络和重试设置 - 更保守的设置
        'retries': 5,  # 减少重试次数，快速失败
        'fragment_retries': 5,  # 片段重试次数
        'socket_timeout': 30,  # 套接字超时
        'http_chunk_size': 10485760,  # 10MB chunks

        # 减少请求间隔
        'sleep_interval': 0,  # 移除请求间隔
        'max_sleep_interval': 0,  # 移除最大睡眠间隔
        'sleep_interval_subtitles': 0,  # 字幕请求间隔

        # 简化请求头信息
        'http_headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        },

        # 使用更稳定的客户端配置
        'extractor_args': {
            'youtube': {
                'player_client': ['web'],  # 只使用web客户端，最稳定
                'skip': ['hls'],  # 跳过容易出问题的HLS流
            }
        },

        # 后处理器 - 确保MP4格式
        'postprocessors': get_postprocessors(),

        # 错误处理
        'ignoreerrors': False,  # 不忽略错误，但会在下面的代码中处理
        'no_warnings': False,
    }

    # 使用 yt-dlp 下载视频，增强错误处理
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            print("\n开始下载...")
            print("正在获取视频信息...")

            # 先获取视频信息
            info = ydl.extract_info(url, download=False)

            # 检查格式数量
            formats = info.get('formats', [])
            print(f"找到 {len(formats)} 个格式")
            print(f"视频标题: {info.get('title', 'Unknown')}")
            print(f"视频时长: {info.get('duration', 'Unknown')} 秒")
            print(f"上传者: {info.get('uploader', 'Unknown')}")

            # 显示最高可用分辨率信息和格式调试
            formats = info.get('formats', [])
            if formats:
                # 找到最高分辨率
                max_height = 0
                max_format = None
                for fmt in formats:
                    if fmt.get('height') and fmt.get('height') > max_height:
                        max_height = fmt.get('height')
                        max_format = fmt

                if max_format:
                    width = max_format.get('width', 'Unknown')
                    height = max_format.get('height', 'Unknown')
                    fps = max_format.get('fps', 'Unknown')
                    print(f"最高可用分辨率: {width}x{height} @ {fps}fps")
                else:
                    print("分辨率信息: 无法获取")

                # 调试信息：显示所有可用格式
                print("\n=== 格式调试信息 ===")
                print("可用的视频格式:")
                video_formats = []
                for fmt in formats:
                    if fmt.get('vcodec') != 'none':  # 有视频编码的格式
                        height = fmt.get('height', 'Unknown')
                        width = fmt.get('width', 'Unknown')
                        ext = fmt.get('ext', 'Unknown')
                        format_id = fmt.get('format_id', 'Unknown')
                        fps = fmt.get('fps', 'Unknown')
                        filesize = fmt.get('filesize', 0)
                        filesize_mb = f"{filesize/1024/1024:.1f}MB" if filesize else "Unknown"

                        video_formats.append({
                            'id': format_id,
                            'resolution': f"{width}x{height}",
                            'fps': fps,
                            'ext': ext,
                            'size': filesize_mb
                        })

                # 按分辨率排序显示
                video_formats.sort(key=lambda x: int(x['resolution'].split('x')[1]) if x['resolution'].split('x')[1] != 'Unknown' else 0, reverse=True)

                for i, fmt in enumerate(video_formats[:10]):  # 只显示前10个
                    print(f"  {i+1}. ID:{fmt['id']} - {fmt['resolution']} @ {fmt['fps']}fps - {fmt['ext']} - {fmt['size']}")

                if len(video_formats) > 10:
                    print(f"  ... 还有 {len(video_formats)-10} 个格式")
                print("========================\n")

                # 检查是否需要使用备用配置
                max_height = max([int(fmt['resolution'].split('x')[1]) for fmt in video_formats if fmt['resolution'].split('x')[1] != 'Unknown'], default=0)
                if max_height <= 360:
                    print("⚠️  检测到只有低质量格式，尝试备用配置...")

                    # 尝试Android客户端配置
                    try:
                        android_opts = {
                            'quiet': True,
                            'cookiefile': cookies_path if os.path.exists(cookies_path) else None,
                            'extractor_args': {
                                'youtube': {
                                    'player_client': ['android'],
                                    'skip': ['hls'],
                                }
                            }
                        }

                        with yt_dlp.YoutubeDL(android_opts) as android_ydl:
                            android_info = android_ydl.extract_info(url, download=False)
                            android_formats = android_info.get('formats', [])
                            android_max_height = max([fmt.get('height', 0) for fmt in android_formats], default=0)

                            if android_max_height > max_height:
                                print(f"✓ Android配置找到更高质量: {android_max_height}p")
                                info = android_info
                                info['_use_android'] = True
                            else:
                                print("Android配置没有找到更高质量")

                    except Exception as e:
                        print(f"Android配置失败: {e}")
                        print("继续使用原始配置")
            else:
                print("格式信息: 无法获取")

            # 检查是否使用Android配置
            if '_use_android' in info:
                print("使用Android配置进行下载...")

                # 创建Android下载配置
                download_opts = {
                    'format': 'bestvideo[height<=1080]+bestaudio/best[height<=1080]/best',
                    'merge_output_format': 'mp4',
                    'outtmpl': output_template,
                    'noplaylist': True,
                    'quiet': False,
                    'ffmpeg_location': ffmpeg_path,
                    'cookiefile': cookies_path if os.path.exists(cookies_path) else None,

                    # 网络设置
                    'retries': 3,
                    'fragment_retries': 3,
                    'socket_timeout': 30,
                    'http_chunk_size': 10485760,  # 10MB chunks

                    'http_headers': {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': '*/*',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Connection': 'keep-alive',
                    },

                    'extractor_args': {
                        'youtube': {
                            'player_client': ['android'],
                            'skip': ['hls'],
                        }
                    },

                    'postprocessors': get_postprocessors(),
                }

                # 询问是否强制重新下载
                print("\n注意: 如果文件已存在，可能不会重新下载")
                choice = input("是否强制重新下载? (y/n): ").lower().strip()
                if choice == 'y':
                    download_opts['force_overwrites'] = True
                    print("将强制重新下载...")

                print("\n开始下载...")

                try:
                    with yt_dlp.YoutubeDL(download_opts) as download_ydl:
                        download_ydl.download([url])
                except Exception as download_error:
                    print(f"下载失败: {download_error}")

                    print("尝试使用备用下载策略...")

                    # 简化的备用策略
                    fallback_opts = {
                        'format': 'best[height<=720]/best',  # 降低分辨率要求
                        'merge_output_format': 'mp4',
                        'outtmpl': output_template,
                        'noplaylist': True,
                        'quiet': False,
                        'ffmpeg_location': ffmpeg_path,
                        'cookiefile': cookies_path if os.path.exists(cookies_path) else None,

                        'retries': 3,
                        'fragment_retries': 3,
                        'socket_timeout': 30,

                        'http_headers': {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': '*/*',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Connection': 'keep-alive',
                        },

                        'extractor_args': {
                            'youtube': {
                                'player_client': ['web'],
                            }
                        },

                        'postprocessors': get_postprocessors(),
                    }

                    if choice == 'y':
                        fallback_opts['force_overwrites'] = True

                    try:
                        print("使用备用策略下载...")
                        with yt_dlp.YoutubeDL(fallback_opts) as fallback_ydl:
                            fallback_ydl.download([url])
                        print("备用策略下载成功！")
                        return
                    except Exception as fallback_error:
                        print(f"备用策略也失败: {fallback_error}")
                        print("所有下载策略都失败了")
            else:
                # 使用原始配置下载
                print("使用原始配置下载...")
                try:
                    ydl.download([url])
                except Exception as original_error:
                    print(f"原始配置下载失败: {original_error}")

                    # 最后的备用策略
                    print("尝试最简单的下载配置...")
                    simple_opts = {
                        'format': 'best',
                        'outtmpl': output_template,
                        'noplaylist': True,
                        'ffmpeg_location': ffmpeg_path,
                        'postprocessors': get_postprocessors(),
                    }

                    try:
                        with yt_dlp.YoutubeDL(simple_opts) as simple_ydl:
                            simple_ydl.download([url])
                        print("简单配置下载成功！")
                    except Exception as simple_error:
                        print(f"所有下载方式都失败了: {simple_error}")
                        return

            print(f"下载完成！视频已保存到文件夹：{folder_path}\n")

        except yt_dlp.utils.DownloadError as e:
            print(f"下载错误：{e}")
            print("尝试使用备用格式下载...")

            # 简化的备用格式
            backup_opts = {
                'format': 'best[height<=720]/best',  # 降低分辨率要求
                'outtmpl': output_template,
                'noplaylist': True,
                'ffmpeg_location': ffmpeg_path,
                'cookiefile': cookies_path if os.path.exists(cookies_path) else None,
                'extractor_args': {
                    'youtube': {
                        'player_client': ['web'],
                    }
                },
                'postprocessors': get_postprocessors(),
            }

            try:
                with yt_dlp.YoutubeDL(backup_opts) as backup_ydl:
                    backup_ydl.download([url])
                    print(f"备用格式下载完成！视频已保存到文件夹：{folder_path}\n")
            except Exception as backup_e:
                print(f"备用格式下载失败：{backup_e}")
                print(f"所有下载方式都失败了\n")

        except Exception as e:
            print(f"下载失败：{e}")
            print("可能的原因：")
            print("1. 网络连接问题")
            print("2. 视频地区限制")
            print("3. 视频已被删除或设为私有")
            print("4. 需要更新 yt-dlp 版本")
            print("请检查网络连接后重试\n")

def check_yt_dlp_version():
    """检查yt-dlp版本"""
    try:
        print(f"当前 yt-dlp 版本: {yt_dlp.version.__version__}")
    except:
        print("无法获取 yt-dlp 版本信息")

def check_cookies_status():
    """检查cookies文件状态"""
    cookies_path = os.path.join(os.path.dirname(__file__), "youtube_cookies.txt")
    if os.path.exists(cookies_path):
        # 获取文件修改时间
        import time
        mtime = os.path.getmtime(cookies_path)
        mtime_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(mtime))
        print(f"✓ 已找到cookies文件 (更新时间: {mtime_str})")
        return True
    else:
        print("✗ 未找到cookies文件")
        print("  提示: 运行 youtube登录.py 来获取cookies文件")
        return False

def upgrade_yt_dlp():
    """升级yt-dlp到最新版本"""
    print("\n=== 升级 yt-dlp ===")
    print("正在升级 yt-dlp 到最新版本...")

    import subprocess
    import sys

    try:
        # 执行升级命令
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'yt-dlp'],
                              capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            print("✓ yt-dlp 升级成功！")
            print(result.stdout)

            # 显示新版本信息
            try:
                import importlib
                import yt_dlp
                importlib.reload(yt_dlp)
                print(f"当前版本: {yt_dlp.version.__version__}")
            except:
                print("版本信息获取失败，但升级应该已完成")
        else:
            print("✗ yt-dlp 升级失败！")
            print("错误信息:", result.stderr)

    except subprocess.TimeoutExpired:
        print("✗ 升级超时，请检查网络连接")
    except Exception as e:
        print(f"✗ 升级过程中出现错误: {e}")

    print("\n按回车键继续...")
    input()

def show_main_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("        YouTube 视频下载器")
    print("="*50)
    print("请选择操作：")
    print("1. 升级 yt-dlp 到最新版本")
    print("2. 下载 YouTube 视频")
    print("3. 查看帮助信息")
    print("4. 查看版本信息")
    print("0. 退出程序")
    print("="*50)

def show_help():
    """显示帮助信息"""
    print("\n=== YouTube 视频下载器 ===")
    print("支持的 URL 格式：")
    print("- https://www.youtube.com/watch?v=...")
    print("- https://youtu.be/...")
    print("- https://m.youtube.com/watch?v=...")
    print("\n下载设置：")
    print("- 自动下载最高可用分辨率（最高1080P）")
    print("- 优先选择H.264编码，确保Premiere Pro兼容性")
    print("- 输出MP4格式，支持所有主流剪辑软件")
    print("- 视频和音频自动合并为单个文件")
    print("\n下载的视频将保存到桌面的 'YouTube素材' 文件夹中")
    print("\n按回车键继续...")
    input()

def download_mode():
    """下载模式"""
    # 设置桌面路径和目标文件夹路径
    desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
    folder_path = os.path.join(desktop_path, 'YouTube素材')

    # 创建文件夹
    create_folder(folder_path)

    print("\n=== 下载模式 ===")
    check_yt_dlp_version()
    check_cookies_status()
    print()

    while True:
        # 动态输入 YouTube 视频 URL
        user_input = input("请输入 YouTube 视频 URL（输入 'back' 返回主菜单）：").strip()

        if user_input.lower() == 'back':
            break
        elif not user_input:
            print("没有输入 URL，请重新尝试。\n")
        elif 'youtube.com' in user_input or 'youtu.be' in user_input:
            download_video(user_input, folder_path)
        else:
            print("请输入有效的 YouTube URL。\n")

if __name__ == '__main__':
    while True:
        show_main_menu()

        try:
            choice = input("\n请选择操作 (0-4): ").strip()

            if choice == '0':
                print("程序已退出！")
                break
            elif choice == '1':
                upgrade_yt_dlp()
            elif choice == '2':
                download_mode()
            elif choice == '3':
                show_help()
            elif choice == '4':
                check_yt_dlp_version()
                print("\n按回车键继续...")
                input()
            else:
                print("无效选择，请输入 0-4 之间的数字。")
                print("按回车键继续...")
                input()

        except KeyboardInterrupt:
            print("\n\n程序已退出！")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            print("按回车键继续...")
            input()
