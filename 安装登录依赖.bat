@echo off
chcp 65001 >nul
echo =================================
echo    YouTube登录工具 - 依赖安装
echo =================================
echo.

echo 正在安装selenium库...
pip install selenium

echo.
echo 正在安装webdriver-manager（自动管理ChromeDriver）...
pip install webdriver-manager

echo.
echo =================================
echo 安装完成！
echo =================================
echo.
echo 注意事项：
echo 1. 请确保已安装Chrome浏览器
echo 2. 如果遇到ChromeDriver问题，程序会自动下载
echo 3. 现在可以运行 youtube登录.py 进行登录
echo.
pause
