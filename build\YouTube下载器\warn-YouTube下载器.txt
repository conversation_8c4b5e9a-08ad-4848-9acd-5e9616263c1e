
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), getpass (delayed), netrc (delayed, conditional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), http.server (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _sha512 - imported by random (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), yt_dlp.utils._utils (conditional, optional), pty (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by site (delayed, optional), rlcompleter (optional), websockets.__main__ (optional), code (delayed, conditional, optional)
missing module named _typeshed - imported by setuptools._distutils.dist (conditional), pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named yt_dlp.utils.deprecation_warning - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.networking.common (top-level), yt_dlp.downloader.common (top-level), yt_dlp.postprocessor.common (top-level), yt_dlp.postprocessor.ffmpeg (top-level), yt_dlp.extractor.common (top-level), yt_dlp.YoutubeDL (top-level), yt_dlp.postprocessor (top-level), yt_dlp.options (top-level)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named collections.Callable - imported by collections (optional), socks (optional), cffi.api (optional)
missing module named _dummy_thread - imported by cffi.lock (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by simplejson.compat (conditional, optional), cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named imp - imported by simplejson.compat (conditional), Cryptodome.Util._raw_api (conditional), cffi.verifier (conditional), cffi._imp_emulation (optional), Crypto.Util._raw_api (conditional)
missing module named StringIO - imported by simplejson.compat (conditional, optional), Cryptodome.Util.py3compat (conditional), Crypto.Util.py3compat (conditional)
missing module named 'curl_cffi.const' - imported by yt_dlp.networking._curlcffi (top-level)
missing module named 'curl_cffi.requests' - imported by yt_dlp.networking._curlcffi (top-level)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named brotlicffi - imported by yt_dlp.dependencies (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named UserDict - imported by simplejson.ordered_dict (top-level)
missing module named chardet - imported by requests.compat (optional), requests (optional), requests.packages (optional), requests.help (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named curl_cffi - imported by yt_dlp.dependencies (optional), yt_dlp (top-level)
missing module named xattr - imported by yt_dlp.dependencies (optional)
missing module named secretstorage - imported by yt_dlp.dependencies (optional), yt_dlp (top-level)
missing module named yt_dlp.utils.version_tuple - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.cache (top-level)
missing module named yt_dlp.utils.system_identifier - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.YoutubeDL (top-level)
missing module named yt_dlp.utils.shell_quote - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.downloader.common (top-level), yt_dlp.postprocessor.ffmpeg (top-level), yt_dlp.postprocessor.embedthumbnail (top-level), yt_dlp.postprocessor.exec (top-level), yt_dlp.extractor.openload (top-level), yt_dlp.YoutubeDL (top-level), yt_dlp.postprocessor.sponskrub (top-level), yt_dlp (top-level), yt_dlp.compat._deprecated (delayed)
missing module named yt_dlp.utils.remove_end - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.extractor.youtube._video (top-level), yt_dlp.extractor.allocine (top-level), yt_dlp.extractor.biobiochiletv (top-level), yt_dlp.extractor.charlierose (top-level), yt_dlp.extractor.crtvg (top-level), yt_dlp.extractor.senategov (top-level), yt_dlp.extractor.dispeak (top-level), yt_dlp.extractor.ebay (top-level), yt_dlp.extractor.gamestar (top-level), yt_dlp.extractor.goplay (top-level), yt_dlp.extractor.gopro (top-level), yt_dlp.extractor.hellporno (top-level), yt_dlp.extractor.hungama (top-level), yt_dlp.extractor.icareus (top-level), yt_dlp.extractor.lifenews (top-level), yt_dlp.extractor.mailru (top-level), yt_dlp.extractor.mediastream (top-level), yt_dlp.extractor.mojevideo (top-level), yt_dlp.extractor.motherless (top-level), yt_dlp.extractor.murrtube (top-level), yt_dlp.extractor.nbc (top-level), yt_dlp.extractor.ndtv (top-level), yt_dlp.extractor.nhk (top-level), yt_dlp.extractor.nitter (top-level), yt_dlp.extractor.nytimes (top-level), yt_dlp.extractor.nzonscreen (top-level), yt_dlp.extractor.orf (top-level), yt_dlp.extractor.pinkbike (top-level), yt_dlp.extractor.rheinmaintv (top-level), yt_dlp.extractor.teamtreehouse (top-level), yt_dlp.extractor.theholetv (top-level), yt_dlp.extractor.tv2 (top-level), yt_dlp.extractor.tvw (top-level), yt_dlp.extractor.twitter (top-level), yt_dlp.extractor.viu (top-level), yt_dlp.extractor.vuclip (top-level), yt_dlp.downloader.external (top-level), yt_dlp.options (top-level)
missing module named yt_dlp.utils.format_field - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.extractor.common (top-level), yt_dlp.extractor.youtube.pot._director (top-level), yt_dlp.extractor.openload (top-level), yt_dlp.extractor.youtube._video (top-level), yt_dlp.extractor.youtube._tab (top-level), yt_dlp.extractor.generic (top-level), yt_dlp.extractor.acfun (top-level), yt_dlp.extractor.arnes (top-level), yt_dlp.extractor.awaan (top-level), yt_dlp.extractor.kaltura (top-level), yt_dlp.extractor.banbye (top-level), yt_dlp.extractor.bandlab (top-level), yt_dlp.extractor.bibeltv (top-level), yt_dlp.extractor.bilibili (top-level), yt_dlp.extractor.bitchute (top-level), yt_dlp.extractor.bluesky (top-level), yt_dlp.extractor.bundestag (top-level), yt_dlp.extractor.facebook (top-level), yt_dlp.extractor.cbsnews (top-level), yt_dlp.extractor.flickr (top-level), yt_dlp.extractor.floatplane (top-level), yt_dlp.extractor.francetv (top-level), yt_dlp.extractor.rumble (top-level), yt_dlp.extractor.gamejolt (top-level), yt_dlp.extractor.instagram (top-level), yt_dlp.extractor.iqiyi (top-level), yt_dlp.extractor.joj (top-level), yt_dlp.extractor.vidio (top-level), yt_dlp.extractor.livestreamfails (top-level), yt_dlp.extractor.lnk (top-level), yt_dlp.extractor.medaltv (top-level), yt_dlp.extractor.minds (top-level), yt_dlp.extractor.musicdex (top-level), yt_dlp.extractor.nubilesporn (top-level), yt_dlp.extractor.peertube (top-level), yt_dlp.extractor.pornhub (top-level), yt_dlp.extractor.radlive (top-level), yt_dlp.extractor.rokfin (top-level), yt_dlp.extractor.servus (top-level), yt_dlp.extractor.storyfire (top-level), yt_dlp.extractor.telegram (top-level), yt_dlp.extractor.telewebion (top-level), yt_dlp.extractor.tiktok (top-level), yt_dlp.extractor.trovo (top-level), yt_dlp.extractor.truth (top-level), yt_dlp.extractor.tube8 (top-level), yt_dlp.extractor.twitter (top-level), yt_dlp.extractor.vidlii (top-level), yt_dlp.extractor.wykop (top-level), yt_dlp.extractor.younow (top-level), yt_dlp.extractor.zhihu (top-level), yt_dlp.YoutubeDL (top-level), yt_dlp.options (top-level), yt_dlp (top-level)
missing module named yt_dlp.utils.Popen - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.cookies (top-level), yt_dlp.postprocessor.ffmpeg (top-level), yt_dlp.postprocessor.embedthumbnail (top-level), yt_dlp.postprocessor.exec (top-level), yt_dlp.downloader.rtmp (top-level), yt_dlp.extractor.common (top-level), yt_dlp.extractor.openload (top-level), yt_dlp.YoutubeDL (top-level), yt_dlp.postprocessor.sponskrub (top-level), yt_dlp.downloader.external (top-level)
missing module named yt_dlp.utils.NO_DEFAULT - imported by yt_dlp.utils (top-level), yt_dlp.update (top-level), yt_dlp.downloader (top-level), yt_dlp.downloader.common (top-level), yt_dlp.extractor.common (top-level), yt_dlp.extractor.youtube.pot._provider (top-level), yt_dlp.jsinterp (top-level), yt_dlp.extractor.youtube._video (top-level), yt_dlp.extractor.youtube._tab (top-level), yt_dlp.extractor.adobepass (top-level), yt_dlp.extractor.onet (top-level), yt_dlp.extractor.drtuber (top-level), yt_dlp.extractor.heise (top-level), yt_dlp.extractor.pornhub (top-level), yt_dlp.extractor.tvn24 (top-level), yt_dlp.extractor.xnxx (top-level), yt_dlp.YoutubeDL (top-level), yt_dlp (top-level)
