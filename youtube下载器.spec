# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 主程序分析
a = Analysis(
    ['youtube下载.py'],
    pathex=[],
    binaries=[
        # 包含ffmpeg二进制文件
        ('ffmpeg/bin/ffmpeg.exe', 'ffmpeg/bin'),
        ('ffmpeg/bin/ffprobe.exe', 'ffmpeg/bin'),
        ('ffmpeg/bin/ffplay.exe', 'ffmpeg/bin'),
    ],
    datas=[
        # 包含ffmpeg相关文件
        ('ffmpeg/LICENSE', 'ffmpeg'),
        ('ffmpeg/README.txt', 'ffmpeg'),
        ('ffmpeg/presets', 'ffmpeg/presets'),
        # 包含cookies文件（如果存在）
        ('youtube_cookies.txt', '.'),
    ],
    hiddenimports=[
        'yt_dlp',
        'yt_dlp.extractor',
        'yt_dlp.downloader',
        'yt_dlp.postprocessor',
        'subprocess',
        'importlib',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='YouTube下载器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
