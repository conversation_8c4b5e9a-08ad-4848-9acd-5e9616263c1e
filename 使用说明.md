# YouTube下载器 使用说明

## 快速开始

### 方法1：使用exe文件（推荐）
1. 双击运行 `打包exe.bat` 来创建exe文件
2. 打包完成后，双击 `启动YouTube下载器.bat` 来启动程序
3. 或者直接运行 `dist\YouTube下载器.exe`

### 方法2：直接运行Python脚本
1. 确保已安装Python 3.7+
2. 运行 `pip install -r requirements.txt` 安装依赖
3. 双击 `启动YouTube下载器.bat` 或直接运行 `python youtube下载.py`

## 功能说明

### 主菜单选项
- **选项1**：升级 yt-dlp 到最新版本
- **选项2**：下载 YouTube 视频
- **选项3**：查看帮助信息
- **选项4**：查看版本信息
- **选项0**：退出程序

### 下载功能
- 支持1080P高清下载
- 自动选择H.264编码（Premiere Pro兼容）
- 输出MP4格式
- 视频保存到桌面的"YouTube素材"文件夹

## 文件结构

```
youtube下载器0624/
├── youtube下载.py          # 主程序
├── youtube登录.py          # 登录工具（可选）
├── youtube_cookies.txt     # 登录cookies（可选）
├── ffmpeg/                 # FFmpeg工具包
├── 打包exe.bat            # 打包脚本
├── 启动YouTube下载器.bat   # 启动器
├── youtube下载器.spec     # PyInstaller配置
├── requirements.txt       # Python依赖
└── 使用说明.md           # 本文件
```

## 打包说明

### 打包内容
exe文件会包含以下内容：
- Python运行环境
- yt-dlp库及其依赖
- FFmpeg完整工具包
- cookies文件（如果存在）

### 打包后的优势
- 无需安装Python环境
- 无需安装FFmpeg
- 一个exe文件包含所有功能
- 可以在任何Windows电脑上运行

## 常见问题

### Q: exe文件很大怎么办？
A: 这是正常的，因为包含了完整的Python环境和FFmpeg工具包

### Q: 首次启动很慢？
A: 首次启动需要解压内置文件，后续启动会更快

### Q: 下载失败怎么办？
A: 选择菜单选项1升级yt-dlp到最新版本

### Q: 需要登录YouTube吗？
A: 大部分视频无需登录，如需下载会员视频可运行youtube登录.py

## 更新说明

- 支持菜单式操作界面
- 集成yt-dlp升级功能
- 优化下载配置和错误处理
- 支持一键打包exe文件
