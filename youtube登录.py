#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube登录工具
用于登录YouTube并保存cookies文件，供下载器使用
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def setup_chrome_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    
    # 设置用户数据目录，保持登录状态
    user_data_dir = os.path.join(os.getcwd(), "chrome_user_data")
    chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
    
    # 其他有用的选项
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 设置窗口大小
    chrome_options.add_argument("--window-size=1200,800")
    
    try:
        # 尝试使用系统的Chrome驱动
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    except Exception as e:
        print(f"Chrome驱动启动失败: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        return None

def login_youtube(driver):
    """登录YouTube"""
    try:
        print("正在打开YouTube...")
        driver.get("https://www.youtube.com")
        
        # 等待页面加载
        time.sleep(3)
        
        # 查找登录按钮
        try:
            # 尝试找到登录按钮
            login_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, 'accounts.google.com')]"))
            )
            print("找到登录按钮，点击登录...")
            login_button.click()
        except:
            print("未找到登录按钮，可能已经登录或页面结构变化")
            print("请手动点击登录按钮")
        
        print("\n=== 请在浏览器中完成登录 ===")
        print("1. 输入你的Google账号和密码")
        print("2. 完成任何必要的验证（如2FA）")
        print("3. 确保成功登录到YouTube")
        print("4. 登录完成后，在此处按回车键继续...")
        
        input("按回车键继续...")
        
        # 验证是否登录成功
        driver.get("https://www.youtube.com")
        time.sleep(3)
        
        # 检查是否有用户头像或登录状态
        try:
            # 查找用户头像或账户菜单
            avatar = driver.find_element(By.ID, "avatar-btn")
            print("✓ 登录验证成功！")
            return True
        except:
            try:
                # 备用检查方法
                account_menu = driver.find_element(By.XPATH, "//button[@id='avatar-btn']")
                print("✓ 登录验证成功！")
                return True
            except:
                print("⚠ 无法验证登录状态，但将继续保存cookies")
                return True
                
    except Exception as e:
        print(f"登录过程出错: {e}")
        return False

def save_cookies(driver, filename="youtube_cookies.txt"):
    """保存cookies到文件"""
    try:
        cookies = driver.get_cookies()
        
        # 保存为Netscape格式（yt-dlp支持的格式）
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# Netscape HTTP Cookie File\n")
            f.write("# This is a generated file! Do not edit.\n\n")
            
            for cookie in cookies:
                domain = cookie.get('domain', '')
                flag = 'TRUE' if domain.startswith('.') else 'FALSE'
                path = cookie.get('path', '/')
                secure = 'TRUE' if cookie.get('secure', False) else 'FALSE'
                expires = str(int(cookie.get('expiry', time.time() + 365*24*3600)))
                name = cookie.get('name', '')
                value = cookie.get('value', '')
                
                # 只保存YouTube相关的cookies
                if 'youtube.com' in domain or 'google.com' in domain:
                    f.write(f"{domain}\t{flag}\t{path}\t{secure}\t{expires}\t{name}\t{value}\n")
        
        print(f"✓ Cookies已保存到: {filename}")
        return True
        
    except Exception as e:
        print(f"保存cookies失败: {e}")
        return False

def main():
    """主函数"""
    print("=== YouTube登录工具 ===\n")
    
    # 检查是否需要安装selenium
    try:
        import selenium
    except ImportError:
        print("错误：未安装selenium库")
        print("请运行: pip install selenium")
        input("按回车键退出...")
        return
    
    print("正在启动Chrome浏览器...")
    driver = setup_chrome_driver()
    
    if not driver:
        print("无法启动浏览器，请检查Chrome和ChromeDriver是否正确安装")
        input("按回车键退出...")
        return
    
    try:
        # 登录YouTube
        if login_youtube(driver):
            print("\n正在保存cookies...")
            
            # 保存cookies
            if save_cookies(driver, "youtube_cookies.txt"):
                print("\n=== 登录完成 ===")
                print("cookies文件已保存为: youtube_cookies.txt")
                print("现在可以在youtube下载.py中使用这个cookies文件")
            else:
                print("cookies保存失败")
        else:
            print("登录失败")
            
    except Exception as e:
        print(f"程序执行出错: {e}")
    
    finally:
        print("\n5秒后关闭浏览器...")
        time.sleep(5)
        driver.quit()
        print("浏览器已关闭")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
