import yt_dlp
import os

def create_folder(folder_path):
    """在桌面创建一个文件夹，如果不存在则创建"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"文件夹 '{folder_path}' 已创建！")
    else:
        print(f"文件夹 '{folder_path}' 已存在！")

def download_video(url, folder_path):
    # 确保下载文件保存在目标文件夹
    output_template = os.path.join(folder_path, '%(title)s.%(ext)s')

    # FFmpeg 路径（项目目录中的 ffmpeg 文件夹）
    ffmpeg_path = os.path.join(os.path.dirname(__file__), "ffmpeg", "bin", "ffmpeg.exe")

    # Cookies 文件路径
    cookies_path = os.path.join(os.path.dirname(__file__), "youtube_cookies.txt")

    # 检查是否需要H.264转换的函数
    def get_postprocessors():
        return [
            {
                'key': 'FFmpegVideoConvertor',
                'preferedformat': 'mp4',
            }
        ]

    # 配置 yt-dlp 下载参数 - 下载最大分辨率
    ydl_opts = {
        # 下载1080P质量的视频，优先H.264编码(PR兼容)
        'format': 'bestvideo[height<=1080][vcodec*=avc1]+bestaudio/bestvideo[height<=1080][vcodec*=h264]+bestaudio/bestvideo[height<=1080]+bestaudio/best[height<=1080]/best',
        'merge_output_format': 'mp4',  # 输出为 mp4 格式
        'outtmpl': output_template,  # 保存路径
        'noplaylist': True,  # 不下载整个播放列表
        'quiet': False,  # 显示详细输出（调试时有帮助）
        'ffmpeg_location': ffmpeg_path,  # 手动指定 FFmpeg 路径

        # 使用cookies文件（如果存在）
        'cookiefile': cookies_path if os.path.exists(cookies_path) else None,

        # 网络和重试设置
        'retries': 10,  # 重试次数
        'fragment_retries': 10,  # 片段重试次数
        'socket_timeout': 30,  # 套接字超时
        'http_chunk_size': 10485760,  # 10MB chunks

        # 额外的网络配置来避免403错误
        'sleep_interval': 1,  # 请求间隔
        'max_sleep_interval': 5,  # 最大睡眠间隔
        'sleep_interval_subtitles': 0,  # 字幕请求间隔

        # 添加更完整的请求头信息，模拟真实浏览器
        'http_headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        },

        # 额外的网络设置来避免403错误
        'extractor_args': {
            'youtube': {
                'skip': [],  # 不跳过任何格式
                'player_client': ['web', 'android_creator', 'tv_embedded'],  # 尝试不同的客户端组合
                'formats': 'missing_pot',  # 启用缺少PO Token的格式
                'innertube_host': 'www.youtube.com',  # 强制使用主域名
                'innertube_key': None,  # 让yt-dlp自动获取key
            }
        },

        # 后处理器 - 确保MP4格式
        'postprocessors': get_postprocessors(),

        # 错误处理
        'ignoreerrors': False,  # 不忽略错误，但会在下面的代码中处理
        'no_warnings': False,
    }

    # 使用 yt-dlp 下载视频，增强错误处理
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            print("\n开始下载...")
            print("正在获取视频信息...")

            # 先获取视频信息
            info = ydl.extract_info(url, download=False)

            # 如果格式太少，尝试用不同的配置重新获取
            formats = info.get('formats', [])
            if len(formats) <= 2:  # 如果只有很少的格式
                print("检测到格式数量较少，尝试使用备用配置获取更多格式...")

                # 创建一个更激进的配置
                aggressive_opts = {
                    'quiet': True,
                    'cookiefile': cookies_path if os.path.exists(cookies_path) else None,
                    'extractor_args': {
                        'youtube': {
                            'skip': [],
                            'player_client': ['android_creator'],  # 只用一个可能工作的客户端
                            'formats': 'missing_pot',
                        }
                    }
                }

                try:
                    with yt_dlp.YoutubeDL(aggressive_opts) as aggressive_ydl:
                        aggressive_info = aggressive_ydl.extract_info(url, download=False)
                        aggressive_formats = aggressive_info.get('formats', [])

                        if len(aggressive_formats) > len(formats):
                            print(f"备用配置找到了 {len(aggressive_formats)} 个格式（原来只有 {len(formats)} 个）")
                            info = aggressive_info  # 使用更好的信息
                        else:
                            print("备用配置没有找到更多格式")
                except Exception as e:
                    print(f"备用配置失败: {e}")
                    print("继续使用原始配置")
            print(f"视频标题: {info.get('title', 'Unknown')}")
            print(f"视频时长: {info.get('duration', 'Unknown')} 秒")
            print(f"上传者: {info.get('uploader', 'Unknown')}")

            # 显示最高可用分辨率信息和格式调试
            formats = info.get('formats', [])
            if formats:
                # 找到最高分辨率
                max_height = 0
                max_format = None
                for fmt in formats:
                    if fmt.get('height') and fmt.get('height') > max_height:
                        max_height = fmt.get('height')
                        max_format = fmt

                if max_format:
                    width = max_format.get('width', 'Unknown')
                    height = max_format.get('height', 'Unknown')
                    fps = max_format.get('fps', 'Unknown')
                    print(f"最高可用分辨率: {width}x{height} @ {fps}fps")
                else:
                    print("分辨率信息: 无法获取")

                # 调试信息：显示所有可用格式
                print("\n=== 格式调试信息 ===")
                print("可用的视频格式:")
                video_formats = []
                for fmt in formats:
                    if fmt.get('vcodec') != 'none':  # 有视频编码的格式
                        height = fmt.get('height', 'Unknown')
                        width = fmt.get('width', 'Unknown')
                        ext = fmt.get('ext', 'Unknown')
                        format_id = fmt.get('format_id', 'Unknown')
                        fps = fmt.get('fps', 'Unknown')
                        filesize = fmt.get('filesize', 0)
                        filesize_mb = f"{filesize/1024/1024:.1f}MB" if filesize else "Unknown"

                        video_formats.append({
                            'id': format_id,
                            'resolution': f"{width}x{height}",
                            'fps': fps,
                            'ext': ext,
                            'size': filesize_mb
                        })

                # 按分辨率排序显示
                video_formats.sort(key=lambda x: int(x['resolution'].split('x')[1]) if x['resolution'].split('x')[1] != 'Unknown' else 0, reverse=True)

                for i, fmt in enumerate(video_formats[:10]):  # 只显示前10个
                    print(f"  {i+1}. ID:{fmt['id']} - {fmt['resolution']} @ {fmt['fps']}fps - {fmt['ext']} - {fmt['size']}")

                if len(video_formats) > 10:
                    print(f"  ... 还有 {len(video_formats)-10} 个格式")
                print("========================\n")

                # 如果仍然只有低质量格式，尝试最后的方法
                max_height = max([int(fmt['resolution'].split('x')[1]) for fmt in video_formats if fmt['resolution'].split('x')[1] != 'Unknown'], default=0)
                if max_height <= 360:
                    print("⚠️  检测到只有低质量格式，尝试最后的解决方案...")

                    # 尝试多种不同的配置
                    alternative_configs = [
                        {
                            'name': '直接下载配置(无流媒体)',
                            'config': {
                                'format': 'best[height<=1080][protocol!*=m3u8][protocol!*=dash]/best[protocol!*=m3u8]/best',
                                'quiet': True,
                                'extractor_args': {
                                    'youtube': {
                                        'player_client': ['web'],
                                        'skip': ['hls', 'dash'],
                                    }
                                }
                            }
                        },
                        {
                            'name': 'H.264优先配置(PR兼容)',
                            'config': {
                                'format': 'bestvideo[height<=1080][vcodec*=avc1]+bestaudio/bestvideo[height<=1080][vcodec*=h264]+bestaudio/best[height<=1080]/best',
                                'quiet': True,
                                'extractor_args': {
                                    'youtube': {
                                        'player_client': ['web'],
                                        'skip': ['hls'],
                                    }
                                }
                            }
                        },
                        {
                            'name': '纯净web配置(跳过HLS)',
                            'config': {
                                'format': 'bestvideo[height<=1080][protocol!*=m3u8]+bestaudio/best[height<=1080][protocol!*=m3u8]/best',
                                'quiet': True,
                                'extractor_args': {
                                    'youtube': {
                                        'player_client': ['web'],
                                        'skip': ['hls'],
                                    }
                                }
                            }
                        },
                        {
                            'name': 'iOS配置(非HLS优先)',
                            'config': {
                                'format': 'bestvideo[height<=1080][protocol!*=m3u8][vcodec*=avc1]+bestaudio[protocol!*=m3u8]/bestvideo[height<=1080][protocol!*=m3u8]+bestaudio[protocol!*=m3u8]/best[height<=1080][protocol!*=m3u8]/best[protocol!*=m3u8]/best',
                                'quiet': True,
                                'extractor_args': {
                                    'youtube': {
                                        'player_client': ['ios'],
                                        'skip': ['hls'],
                                    }
                                }
                            }
                        },
                        {
                            'name': '最简配置(跳过HLS)',
                            'config': {
                                'format': 'best[height<=1080][protocol!*=m3u8]/best[height<=1080]/best',
                                'quiet': True,
                                'extractor_args': {
                                    'youtube': {
                                        'skip': ['hls'],
                                    }
                                }
                            }
                        },
                        {
                            'name': 'Android配置',
                            'config': {
                                'format': 'best[height<=1080]/best',
                                'quiet': True,
                                'extractor_args': {
                                    'youtube': {
                                        'player_client': ['android'],
                                        'skip': ['hls'],
                                    }
                                }
                            }
                        },
                        {
                            'name': '基础360p配置(保底)',
                            'config': {
                                'format': '18/best',  # format 18 通常是360p mp4
                                'quiet': True,
                            }
                        }
                    ]

                    best_config = None
                    best_height = max_height
                    best_score = 0  # 综合评分：分辨率 + 协议类型

                    for alt_config in alternative_configs:
                        try:
                            print(f"尝试{alt_config['name']}...")
                            with yt_dlp.YoutubeDL(alt_config['config']) as alt_ydl:
                                alt_info = alt_ydl.extract_info(url, download=False)
                                alt_formats = alt_info.get('formats', [])
                                alt_max_height = 0
                                has_non_hls = False

                                for fmt in alt_formats:
                                    if fmt.get('height') and fmt.get('height') > alt_max_height:
                                        alt_max_height = fmt.get('height')

                                    # 检查是否有非HLS格式
                                    protocol = fmt.get('protocol', '')
                                    url_fmt = fmt.get('url', '')
                                    if 'm3u8' not in protocol and 'm3u8' not in url_fmt:
                                        has_non_hls = True

                                print(f"  {alt_config['name']}找到最高分辨率: {alt_max_height}p {'(含非HLS格式)' if has_non_hls else '(仅HLS格式)'}")

                                # 计算综合评分：分辨率 + 协议类型加分
                                score = alt_max_height
                                if has_non_hls and alt_max_height >= 720:  # 非HLS的高质量格式优先
                                    score += 10000  # 超高优先级，避免403错误
                                elif has_non_hls:  # 非HLS格式大幅加分
                                    score += 5000

                                if score > best_score:
                                    best_score = score
                                    best_height = alt_max_height
                                    best_config = alt_config
                                    info = alt_info  # 更新info

                        except Exception as e:
                            print(f"  {alt_config['name']}失败: {e}")

                    if best_config:
                        print(f"✓ {best_config['name']}找到了最高质量: {best_height}p (评分: {best_score})")

                        # 如果最佳配置评分低于10000，说明可能都是HLS格式，容易403
                        if best_score < 10000 and best_height > 360:
                            print("⚠️  检测到高质量格式可能都是HLS流，容易遇到403错误")
                            print("建议直接使用360p格式，确保下载成功")

                            choice = input("是否直接下载360p格式避免403错误? (y/n): ").lower().strip()
                            if choice == 'y':
                                # 使用360p保底配置
                                for config in alternative_configs:
                                    if '360p' in config['name']:
                                        best_config = config
                                        best_height = 360
                                        print("✓ 已切换到360p保底配置")
                                        break

                        # 标记使用最佳配置
                        info['_best_config'] = best_config
                    else:
                        print("✗ 所有备用配置都没有找到更高质量")
            else:
                print("格式信息: 无法获取")

            # 检查是否有最佳配置可用
            if '_best_config' in info:
                best_config = info['_best_config']
                print(f"使用{best_config['name']}进行下载...")

                # 创建完整的下载配置，优化速度
                download_opts = best_config['config'].copy()
                download_opts.update({
                    'merge_output_format': 'mp4',
                    'outtmpl': output_template,
                    'noplaylist': True,
                    'quiet': False,
                    'ffmpeg_location': ffmpeg_path,

                    # 网络优化设置 - 快速失败检测
                    'retries': 3,  # 减少重试次数，快速检测失败
                    'fragment_retries': 3,  # 减少片段重试，快速切换策略
                    'socket_timeout': 30,
                    'http_chunk_size': 20971520,  # 20MB chunks for faster download
                    'concurrent_fragment_downloads': 4,  # 并发下载片段

                    # 减少延迟
                    'sleep_interval': 0,  # 移除睡眠间隔
                    'max_sleep_interval': 0,
                    'sleep_interval_subtitles': 0,

                    # 连接优化
                    'http_headers': {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': '*/*',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Connection': 'keep-alive',
                        'Cache-Control': 'no-cache',
                    },

                    'postprocessors': [
                        {
                            'key': 'FFmpegVideoConvertor',
                            'preferedformat': 'mp4',
                        }
                    ],
                })

                # 显示可用格式并让用户选择
                print("正在获取可用格式...")
                try:
                    with yt_dlp.YoutubeDL(download_opts) as format_ydl:
                        format_info = format_ydl.extract_info(url, download=False)

                        # 获取所有视频格式
                        formats = format_info.get('formats', [])
                        video_formats = [fmt for fmt in formats if fmt.get('vcodec') != 'none']

                        # 按分辨率排序
                        video_formats.sort(key=lambda x: x.get('height', 0), reverse=True)

                        print(f"\n找到 {len(formats)} 个格式，显示所有视频格式:")
                        print("=" * 80)

                        format_choices = []
                        for i, fmt in enumerate(video_formats):
                            height = fmt.get('height', 'Unknown')
                            width = fmt.get('width', 'Unknown')
                            format_id = fmt.get('format_id', 'Unknown')
                            ext = fmt.get('ext', 'Unknown')
                            filesize = fmt.get('filesize', 0)
                            filesize_mb = f"{filesize/1024/1024:.1f}MB" if filesize else "Unknown"
                            protocol = fmt.get('protocol', 'Unknown')
                            url_sample = fmt.get('url', '')[:50] + '...' if fmt.get('url') else 'No URL'

                            is_hls = 'm3u8' in protocol or 'm3u8' in url_sample
                            protocol_type = "HLS" if is_hls else "Direct"
                            vcodec = fmt.get('vcodec', 'Unknown')

                            # 判断编码类型和兼容性
                            if 'avc1' in vcodec or 'h264' in vcodec:
                                codec_info = "H.264(PR兼容)"
                                compat_icon = "✓"
                            elif 'vp9' in vcodec or 'vp09' in vcodec:
                                codec_info = "VP9(可能不兼容PR)"
                                compat_icon = "⚠"
                            elif 'av01' in vcodec:
                                codec_info = f"AV1({vcodec})"
                                compat_icon = "⚠"
                            else:
                                codec_info = f"{vcodec}"
                                compat_icon = "?"

                            # 添加推荐标记
                            recommend = ""
                            if height >= 1080 and 'h264' in vcodec.lower() and not is_hls:
                                recommend = " [推荐]"
                            elif height >= 720 and 'h264' in vcodec.lower():
                                recommend = " [较好]"

                            print(f"  {i+1:2d}. {compat_icon} ID:{format_id} - {width}x{height} - {ext} - {filesize_mb:>10} - {protocol_type:>6} - {codec_info}{recommend}")
                            format_choices.append(fmt)

                        print("=" * 80)
                        print("说明: ✓=PR兼容 ⚠=可能不兼容 ?=未知 [推荐]=最佳选择 [较好]=次佳选择")

                        # 让用户选择格式
                        while True:
                            try:
                                print(f"\n请选择要下载的格式 (1-{len(video_formats)}):")
                                print("输入 'auto' 自动选择最佳格式")
                                print("输入 'exit' 取消下载")

                                user_choice = input("请输入选择: ").strip().lower()

                                if user_choice == 'exit':
                                    print("已取消下载")
                                    return
                                elif user_choice == 'auto':
                                    # 自动选择：优先H.264 + 高分辨率 + Direct
                                    selected_format = None
                                    for fmt in video_formats:
                                        vcodec = fmt.get('vcodec', '').lower()
                                        protocol = fmt.get('protocol', '')
                                        url_sample = fmt.get('url', '')
                                        is_hls = 'm3u8' in protocol or 'm3u8' in url_sample

                                        if 'h264' in vcodec and not is_hls and fmt.get('height', 0) >= 720:
                                            selected_format = fmt
                                            break

                                    if not selected_format:
                                        selected_format = video_formats[0]  # 选择最高质量

                                    format_index = video_formats.index(selected_format)
                                    print(f"自动选择: {format_index + 1}. ID:{selected_format.get('format_id')}")
                                    break
                                else:
                                    format_index = int(user_choice) - 1
                                    if 0 <= format_index < len(video_formats):
                                        selected_format = video_formats[format_index]
                                        print(f"已选择: {format_index + 1}. ID:{selected_format.get('format_id')}")
                                        break
                                    else:
                                        print(f"请输入 1 到 {len(video_formats)} 之间的数字")
                            except ValueError:
                                print("请输入有效的数字")

                        # 更新下载配置为用户选择的格式
                        selected_format_id = selected_format.get('format_id')

                        # 检查是否需要音频
                        if selected_format.get('acodec') == 'none':
                            # 需要单独下载音频
                            audio_formats = [fmt for fmt in formats if fmt.get('vcodec') == 'none' and fmt.get('acodec') != 'none']
                            if audio_formats:
                                best_audio = max(audio_formats, key=lambda x: x.get('abr', 0))
                                download_opts['format'] = f"{selected_format_id}+{best_audio.get('format_id')}"
                                print(f"将下载视频格式 {selected_format_id} + 音频格式 {best_audio.get('format_id')}")
                            else:
                                download_opts['format'] = selected_format_id
                                print(f"将下载格式 {selected_format_id} (可能无音频)")
                        else:
                            download_opts['format'] = selected_format_id
                            print(f"将下载格式 {selected_format_id} (包含音频)")

                except Exception as e:
                    print(f"无法获取格式信息: {e}")
                    print("将使用默认格式下载")

                # 询问是否强制重新下载
                print("\n注意: 如果文件已存在，可能不会重新下载")
                choice = input("是否强制重新下载? (y/n): ").lower().strip()
                if choice == 'y':
                    download_opts['force_overwrites'] = True
                    print("将强制重新下载...")

                # 显示速度优化提示
                print("\n=== 下载速度优化提示 ===")
                print("已启用以下优化设置:")
                print("- 并发片段下载: 4个同时下载")
                print("- 大块下载: 20MB分块")
                print("- 移除睡眠间隔")
                print("- 优先选择直接下载格式(非HLS)")
                print("如果速度仍然很慢，可能是:")
                print("1. 网络带宽限制")
                print("2. YouTube服务器限速")
                print("3. 视频只有HLS格式可用")
                print("========================\n")

                try:
                    with yt_dlp.YoutubeDL(download_opts) as download_ydl:
                        download_ydl.download([url])
                except Exception as download_error:
                    error_msg = str(download_error)
                    print(f"使用{best_config['name']}下载失败: {download_error}")

                    # 检查是否是403错误
                    if "403" in error_msg or "Forbidden" in error_msg:
                        print("检测到403错误，可能是HLS流限制")
                        print("尝试使用非HLS备用策略...")

                        # 使用专门的非HLS配置
                        non_hls_opts = {
                            'format': 'best[height<=1080][protocol!*=m3u8]/best[protocol!*=m3u8]/best',
                            'merge_output_format': 'mp4',
                            'outtmpl': output_template,
                            'noplaylist': True,
                            'quiet': False,
                            'ffmpeg_location': ffmpeg_path,
                            'retries': 20,
                            'fragment_retries': 20,
                            'socket_timeout': 120,
                            'http_chunk_size': 52428800,
                            'sleep_interval': 0,
                            'max_sleep_interval': 0,
                            'http_headers': {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                'Accept': '*/*',
                                'Accept-Language': 'en-US,en;q=0.9',
                                'Connection': 'keep-alive',
                            },
                            'extractor_args': {
                                'youtube': {
                                    'player_client': ['web'],
                                    'skip': ['hls', 'dash'],  # 跳过所有流格式
                                }
                            },
                            'postprocessors': get_postprocessors(),
                        }

                        if choice == 'y':
                            non_hls_opts['force_overwrites'] = True

                        try:
                            print("使用非HLS配置下载...")
                            with yt_dlp.YoutubeDL(non_hls_opts) as non_hls_ydl:
                                non_hls_ydl.download([url])
                            print("非HLS配置下载成功！")
                            return  # 成功下载，直接返回
                        except Exception as non_hls_error:
                            print(f"非HLS配置也失败: {non_hls_error}")

                    print("尝试使用最基本的下载策略...")

                    # 备用策略：使用最简配置但添加速度优化
                    fallback_opts = {
                        'format': 'best[height<=1080][protocol!*=m3u8]/best[height<=1080]/best',  # 优先非HLS
                        'merge_output_format': 'mp4',
                        'outtmpl': output_template,
                        'noplaylist': True,
                        'quiet': False,
                        'ffmpeg_location': ffmpeg_path,

                        # 速度优化
                        'retries': 20,
                        'fragment_retries': 20,
                        'socket_timeout': 120,
                        'http_chunk_size': 52428800,  # 50MB chunks for maximum speed
                        'concurrent_fragment_downloads': 8,  # 更多并发
                        'sleep_interval': 0,
                        'max_sleep_interval': 0,

                        'http_headers': {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': '*/*',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Connection': 'keep-alive',
                            'Cache-Control': 'no-cache',
                        },
                        'extractor_args': {
                            'youtube': {
                                'player_client': ['web'],
                                'skip': ['hls'],  # 跳过容易403的HLS流
                            }
                        },
                        'postprocessors': [
                            {
                                'key': 'FFmpegVideoConvertor',
                                'preferedformat': 'mp4',
                            }
                        ],
                    }

                    if choice == 'y':
                        fallback_opts['force_overwrites'] = True

                    try:
                        print("使用备用策略下载...")
                        with yt_dlp.YoutubeDL(fallback_opts) as fallback_ydl:
                            fallback_ydl.download([url])
                    except Exception as fallback_error:
                        print(f"备用策略也失败: {fallback_error}")
                        print("尝试最基本的下载方式...")

                        # 最后的策略：最基本的配置
                        basic_opts = {
                            'format': 'best',
                            'outtmpl': output_template,
                            'noplaylist': True,
                            'ffmpeg_location': ffmpeg_path,
                        }

                        if choice == 'y':
                            basic_opts['force_overwrites'] = True

                        with yt_dlp.YoutubeDL(basic_opts) as basic_ydl:
                            basic_ydl.download([url])
            else:
                # 使用原始配置下载
                print("使用原始配置下载...")
                ydl.download([url])

            print(f"下载完成！视频已保存到文件夹：{folder_path}\n")

        except yt_dlp.utils.DownloadError as e:
            print(f"下载错误：{e}")
            print("尝试使用备用格式下载...")

            # 尝试备用格式 - 限制为1080P，使用简化的格式选择
            backup_opts = ydl_opts.copy()
            backup_opts['format'] = 'bestvideo[height<=1080]+bestaudio/best[height<=1080]'
            backup_opts['extractor_args'] = {
                'youtube': {
                    'skip': ['hls', 'dash'],
                    'player_client': ['web'],  # 只使用web客户端
                }
            }

            try:
                with yt_dlp.YoutubeDL(backup_opts) as backup_ydl:
                    backup_ydl.download([url])
                    print(f"备用格式下载完成！视频已保存到文件夹：{folder_path}\n")
            except Exception as backup_e:
                print(f"备用格式下载失败：{backup_e}")
                print("尝试使用最基本的下载方式...")

                # 最后的备用方案 - 使用最简单的配置但仍然选择最高质量
                simple_opts = {
                    'format': 'best',  # 选择最佳质量，不降级
                    'outtmpl': output_template,
                    'noplaylist': True,
                    'ffmpeg_location': ffmpeg_path,
                    'extractor_args': {
                        'youtube': {
                            'player_client': ['web'],
                        }
                    }
                }

                try:
                    with yt_dlp.YoutubeDL(simple_opts) as simple_ydl:
                        simple_ydl.download([url])
                        print(f"基本格式下载完成！视频已保存到文件夹：{folder_path}\n")
                except Exception as simple_e:
                    print(f"所有下载方式都失败了：{simple_e}\n")

        except Exception as e:
            print(f"下载失败：{e}")
            print("可能的原因：")
            print("1. 网络连接问题")
            print("2. 视频地区限制")
            print("3. 视频已被删除或设为私有")
            print("4. 需要更新 yt-dlp 版本")
            print("请检查网络连接后重试\n")

def check_yt_dlp_version():
    """检查yt-dlp版本"""
    try:
        print(f"当前 yt-dlp 版本: {yt_dlp.version.__version__}")
    except:
        print("无法获取 yt-dlp 版本信息")

def check_cookies_status():
    """检查cookies文件状态"""
    cookies_path = os.path.join(os.path.dirname(__file__), "youtube_cookies.txt")
    if os.path.exists(cookies_path):
        # 获取文件修改时间
        import time
        mtime = os.path.getmtime(cookies_path)
        mtime_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(mtime))
        print(f"✓ 已找到cookies文件 (更新时间: {mtime_str})")
        return True
    else:
        print("✗ 未找到cookies文件")
        print("  提示: 运行 youtube登录.py 来获取cookies文件")
        return False

def show_help():
    """显示帮助信息"""
    print("\n=== YouTube 视频下载器 ===")
    print("支持的命令：")
    print("- 直接输入 YouTube URL 开始下载")
    print("- 输入 'help' 显示此帮助信息")
    print("- 输入 'version' 查看版本信息")
    print("- 输入 'exit' 退出程序")
    print("\n支持的 URL 格式：")
    print("- https://www.youtube.com/watch?v=...")
    print("- https://youtu.be/...")
    print("- https://m.youtube.com/watch?v=...")
    print("\n下载设置：")
    print("- 自动下载最高可用分辨率（最高1080P）")
    print("- 优先选择H.264编码，确保Premiere Pro兼容性")
    print("- 输出MP4格式，支持所有主流剪辑软件")
    print("- 视频和音频自动合并为单个文件")
    print("\n下载的视频将保存到桌面的 'YouTube素材' 文件夹中\n")

if __name__ == '__main__':
    # 设置桌面路径和目标文件夹路径
    desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
    folder_path = os.path.join(desktop_path, 'YouTube素材')

    # 创建文件夹
    create_folder(folder_path)

    # 显示欢迎信息
    print("\n=== YouTube 视频下载器 ===")
    print("输入 'help' 查看帮助信息")
    check_yt_dlp_version()
    check_cookies_status()
    print()

    while True:
        # 动态输入 YouTube 视频 URL
        user_input = input("请输入 YouTube 视频 URL（输入 'exit' 退出）：").strip()

        if user_input.lower() == 'exit':
            print("程序已退出！")
            break
        elif user_input.lower() == 'help':
            show_help()
        elif user_input.lower() == 'version':
            check_yt_dlp_version()
            print()
        elif not user_input:
            print("没有输入 URL，请重新尝试。\n")
        elif 'youtube.com' in user_input or 'youtu.be' in user_input:
            download_video(user_input, folder_path)
        else:
            print("请输入有效的 YouTube URL。输入 'help' 查看支持的格式。\n")
