@echo off
echo ========================================
echo        YouTube Downloader Package Tool
echo ========================================
echo.

echo Checking Python environment...
python --version
if %errorlevel% neq 0 (
    echo Error: Python not found, please install Python first
    pause
    exit /b 1
)

echo.
echo Installing/updating dependencies...
echo ----------------------------------------

echo Installing yt-dlp...
pip install --upgrade yt-dlp
if %errorlevel% neq 0 (
    echo Warning: yt-dlp installation failed
)

echo Installing PyInstaller...
pip install --upgrade pyinstaller
if %errorlevel% neq 0 (
    echo Error: PyInstaller installation failed
    pause
    exit /b 1
)

echo.
echo Starting to package exe file...
echo ----------------------------------------

echo Cleaning previous build files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo Using PyInstaller to package...
pyinstaller --clean "youtube下载器.spec"

if %errorlevel% eq 0 (
    echo.
    echo ========================================
    echo           Package Success!
    echo ========================================
    echo.
    echo exe file location: dist\YouTube下载器.exe
    echo.
    echo Notes:
    echo 1. exe file includes ffmpeg, no additional installation needed
    echo 2. cookies file included if exists
    echo 3. First run may take a few seconds to start
    echo.

    if exist "dist\YouTube下载器.exe" (
        echo Run exe file now for testing? (Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo Starting exe file...
            start "" "dist\YouTube下载器.exe"
        )
    )
) else (
    echo.
    echo ========================================
    echo           Package Failed!
    echo ========================================
    echo.
    echo Please check error messages and retry
)

echo.
pause
