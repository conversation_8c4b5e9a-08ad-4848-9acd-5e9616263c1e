@echo off
title YouTube Downloader

echo ========================================
echo        YouTube Downloader Launcher
echo ========================================
echo.

REM Check if exe file exists
if exist "dist\YouTube下载器.exe" (
    echo Found exe file, starting...
    echo.
    cd /d "%~dp0"
    "dist\YouTube下载器.exe"
) else if exist "YouTube下载器.exe" (
    echo Found exe file, starting...
    echo.
    cd /d "%~dp0"
    "YouTube下载器.exe"
) else (
    echo Exe file not found, trying to run Python script directly...
    echo.

    REM Check Python environment
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo Error: Python environment not found, and no exe file found
        echo.
        echo Please choose one of the following options:
        echo 1. Install Python and run package-exe.bat to create exe file
        echo 2. Or install Python and run youtube下载.py directly
        echo.
        pause
        exit /b 1
    )

    echo Running with Python directly...
    python "youtube下载.py"
)

echo.
echo Program exited
pause
