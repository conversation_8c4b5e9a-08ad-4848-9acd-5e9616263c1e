# YouTube登录和Cookies使用说明

## 📋 概述
通过登录YouTube并保存cookies，可以获得更高质量的视频下载权限，解决1080P下载问题。

## 🚀 使用步骤

### 第一步：安装依赖
1. 双击运行 `安装登录依赖.bat`
2. 等待安装完成

### 第二步：登录YouTube
1. 运行 `python youtube登录.py`
2. 程序会自动打开Chrome浏览器
3. 在浏览器中登录你的YouTube/Google账号
4. 完成登录后，按回车键继续
5. 程序会自动保存cookies到 `youtube_cookies.txt`

### 第三步：使用cookies下载
1. 运行 `python youtube下载.py`
2. 程序会自动检测并使用cookies文件
3. 现在应该能下载到1080P视频了

## 📁 文件说明

### 新增文件
- `youtube登录.py` - YouTube登录脚本
- `安装登录依赖.bat` - 依赖安装脚本
- `youtube_cookies.txt` - 保存的cookies文件（登录后生成）
- `chrome_user_data/` - Chrome用户数据目录（自动生成）

### 修改文件
- `youtube下载.py` - 已添加cookies支持

## ⚠️ 注意事项

### 1. Chrome浏览器要求
- 必须安装Chrome浏览器
- 程序会自动下载ChromeDriver

### 2. 登录安全
- 使用你自己的YouTube账号登录
- cookies文件包含登录信息，请妥善保管
- 不要分享cookies文件给他人

### 3. Cookies有效期
- Cookies有时效性，过期后需要重新登录
- 如果下载质量下降，尝试重新登录

### 4. 网络要求
- 需要能正常访问YouTube
- 登录过程需要稳定的网络连接

## 🔧 故障排除

### 问题1：Chrome启动失败
**解决方案：**
- 确保已安装Chrome浏览器
- 关闭所有Chrome窗口后重试
- 检查是否有杀毒软件阻止

### 问题2：登录失败
**解决方案：**
- 检查网络连接
- 尝试手动在浏览器中登录YouTube
- 确保账号密码正确

### 问题3：Cookies无效
**解决方案：**
- 删除 `youtube_cookies.txt` 文件
- 重新运行登录脚本
- 确保登录过程完整

### 问题4：仍然无法下载1080P
**可能原因：**
- 该视频确实没有1080P版本
- Cookies已过期，需要重新登录
- YouTube的新限制

## 📊 预期效果

### 登录前
- 只能下载360P等低质量视频
- 受到YouTube的访客限制

### 登录后
- 可以下载1080P高质量视频
- 获得与浏览器相同的访问权限
- 减少下载限制和错误

## 🔄 定期维护

建议每月重新登录一次，确保cookies有效性：
1. 删除旧的 `youtube_cookies.txt`
2. 运行 `python youtube登录.py` 重新登录
3. 测试下载效果

## 📞 技术支持

如果遇到问题：
1. 检查Chrome浏览器版本
2. 确保网络连接正常
3. 尝试重新安装依赖
4. 重新登录获取新cookies
